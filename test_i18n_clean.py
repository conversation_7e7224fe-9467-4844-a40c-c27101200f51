#!/usr/bin/env python3
"""
Clean test script to verify the i18n system is working correctly.
"""

from models.i18n_config import get_text, set_locale, get_locale, setup_i18n
from models.config import config

def test_i18n_system():
    """Test the i18n localization system"""
    print("=== Testing i18n Localization System ===\n")
    
    # Initialize the system
    setup_i18n(config.DEFAULT_LANGUAGE)
    print(f"Initialized with default language: {config.DEFAULT_LANGUAGE}")
    print(f"Current locale: {get_locale()}")
    
    # Test getting text in default language (Russian)
    print(f"\nTesting Russian (default):")
    rate_limit_ru = get_text("rate_limit_exceeded")
    print(f"Rate limit message: {rate_limit_ru}")
    
    model_info_ru = get_text("current_model", model_name="DeepSeek Chat")
    print(f"Model info: {model_info_ru}")
    
    # Test switching to English
    print(f"\nTesting English:")
    set_locale("en")
    print(f"Switched to locale: {get_locale()}")
    
    rate_limit_en = get_text("rate_limit_exceeded")
    print(f"Rate limit message: {rate_limit_en}")
    
    model_info_en = get_text("current_model", model_name="DeepSeek Chat")
    print(f"Model info: {model_info_en}")
    
    # Test getting text with specific locale parameter
    print(f"\nTesting locale parameter:")
    help_text_ru = get_text("help_text", locale="ru")
    print(f"Help text (RU): {help_text_ru[:100]}...")
    
    help_text_en = get_text("help_text", locale="en")
    print(f"Help text (EN): {help_text_en[:100]}...")
    
    # Test start greeting
    print(f"\nTesting start greeting:")
    start_ru = get_text("start_greeting", locale="ru", user="@testuser")
    print(f"Start greeting (RU): {start_ru[:100]}...")
    
    start_en = get_text("start_greeting", locale="en", user="@testuser")
    print(f"Start greeting (EN): {start_en[:100]}...")
    
    # Test error handling
    print(f"\nTesting error handling:")
    missing_key = get_text("nonexistent_key")
    print(f"Missing key result: {missing_key}")
    
    print("\n=== i18n System Test Complete ===")
    print("✅ All translations are working correctly!")

if __name__ == "__main__":
    test_i18n_system()
