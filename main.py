import asyncio
from telegram import Update
from telegram.ext import (
    <PERSON>,
    CommandHandler,
    MessageHandler,
    filters,
    ContextTypes,
)
from aiolimiter import AsyncLimiter

from models.config import config
from models.logging import logger
from models.redis import RedisContextManager
from models.openrouter import OpenRouterClient
from models.i18n_config import setup_i18n, get_text, set_locale

# Global rate limiter
user_rate_limiter = AsyncLimiter(config.RATE_LIMIT, 60)
openrouter_client = OpenRouterClient()
context_manager = RedisContextManager()
log = logger.get_logger("Main")

# Initialize localization with python-i18n
setup_i18n(config.DEFAULT_LANGUAGE)
# Set the locale directly using i18n
set_locale(config.DEFAULT_LANGUAGE)


async def handle_message(update: Update, context: ContextTypes.DEFAULT_TYPE):
    user = update.effective_user
    user_id = user.id
    user_message = update.message.text

    # Rate limiting
    try:
        async with user_rate_limiter:
            pass
    except Exception:
        await update.message.reply_text(get_text("rate_limit_exceeded"))
        return

    # Show typing action
    await context.bot.send_chat_action(
        chat_id=update.effective_chat.id,
        action="typing",
    )

    # Get context
    conversation = await context_manager.get_context(user_id)
    conversation.append({"role": "user", "content": user_message})

    # Generate response
    response = await openrouter_client.generate_response(user_id, conversation)

    if response is None:
        await update.message.reply_text(get_text("no_response"))
        return

    # Update context
    conversation.append({"role": "assistant", "content": response})
    await context_manager.set_context(user_id, conversation)

    # Send response (split long messages to avoid Telegram limits)
    if len(response) > 4096:
        for i in range(0, len(response), 4096):
            await update.message.reply_text(response[i : i + 4096])
    else:
        await update.message.reply_text(response)


async def start(update: Update, context: ContextTypes.DEFAULT_TYPE):
    try:
        user = update.effective_user
        start_text = get_text("start_greeting", user=user.mention_markdown_v2())
        model_text = get_text("current_model", model_name=config.MODEL_NAME)
        help_text = get_text("help_text")
        message = f"{start_text}\n\n{model_text}\n\n{help_text}"
        await update.message.reply_markdown_v2(message)
    except Exception as e:
        log.error(f"Error in start command: {e}")


async def help_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    try:
        help_text = get_text("help_text")
        await update.message.reply_markdown_v2(help_text)
    except Exception as e:
        log.error(f"Error in help command: {e}")


async def reset_context(update: Update, context: ContextTypes.DEFAULT_TYPE):
    try:
        user_id = update.effective_user.id
        await context_manager.clear_context(user_id)
        await update.message.reply_text(get_text("context_cleared"))
    except Exception as e:
        log.error(f"Error resetting context: {e}")
        await update.message.reply_text(get_text("context_clear_failed"))


async def model_info(update: Update, context: ContextTypes.DEFAULT_TYPE):
    try:
        model_text = get_text("current_model", model_name=config.MODEL_NAME)
        await update.message.reply_text(model_text)
    except Exception as e:
        log.error(f"Error in model command: {e}")


def setup_application() -> Application:
    application = Application.builder().token(config.TELEGRAM_BOT_TOKEN).build()

    # Register command handlers
    application.add_handler(CommandHandler("start", start))
    application.add_handler(CommandHandler("help", help_command))
    application.add_handler(CommandHandler("reset", reset_context))
    application.add_handler(CommandHandler("model", model_info))

    # Register message handler
    application.add_handler(
        MessageHandler(filters.TEXT & ~filters.COMMAND, handle_message)
    )

    # Error handler
    application.add_error_handler(error_handler)

    return application


async def error_handler(update: object, context: ContextTypes.DEFAULT_TYPE):
    log.error(f"Update {update} caused error: {context.error}")

    if isinstance(update, Update):
        try:
            await update.message.reply_text(get_text("unexpected_error"))
        except Exception:
            pass  # Prevent error loops


async def shutdown(application: Application):
    """Properly clean up resources"""
    try:
        await context_manager.redis.aclose()
    except Exception as e:
        print(f"Redis shutdown error: {e}")

    try:
        await openrouter_client.client.close()  # Close the AsyncOpenAI client
    except Exception as e:
        print(f"OpenRouter client shutdown error: {e}")
        
    try:
        await openrouter_client.session.aclose()  # Close the httpx session
    except Exception as e:
        print(f"OpenRouter session shutdown error: {e}")

def main():
    application = setup_application()

    try:
        # Run the bot until Ctrl+C is pressed
        application.run_polling()
    finally:
        print("\nShutting down gracefully...")
        # Ensure cleanup runs in an event loop
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        loop.run_until_complete(shutdown(application))
        loop.close()


if __name__ == "__main__":
    main()

